import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.operators.AggregateOperator;
import org.apache.flink.api.java.operators.DataSource;
import org.apache.flink.api.java.operators.FlatMapOperator;
import org.apache.flink.api.java.operators.UnsortedGrouping;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.util.Collector;

public class FlowWordCount {
    public static void main(String[] args) throws Exception {
        //1. 创建流式执行环境
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();

        //2. 读取文件
        DataSource<String> lineDS = env.readTextFile("input/words.txt");

        //3. 处理数据
        lineDS.flatMap(
                (String line, Collector<String> words) -> {
                    String[] split = line.split(" ");
                    for (String word : split) {
                        words.collect(word);
                    }
                }
        ).map(word -> Tuple2.of(word, 1L)).groupBy(0).sum(1).print();

        env.execute();
    }
}
