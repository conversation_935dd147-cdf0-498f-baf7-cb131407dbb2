import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.operators.DataSource;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.util.Collector;

/**
 * 改进版的Flink批处理WordCount程序
 * 
 * 功能特性：
 * 1. 支持命令行参数配置输入输出路径
 * 2. 过滤空字符串和标点符号
 * 3. 转换为小写进行统计
 * 4. 按词频降序排序
 * 5. 更好的代码结构和注释
 */
public class ImprovedBatchWordCount {
    
    public static void main(String[] args) throws Exception {
        // 解析命令行参数
        final ParameterTool params = ParameterTool.fromArgs(args);
        
        // 创建执行环境
        final ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        
        // 设置全局参数，使其在所有函数中可用
        env.getConfig().setGlobalJobParameters(params);
        
        // 获取输入路径，默认为input/words.txt
        String inputPath = params.get("input", "input/words.txt");
        String outputPath = params.get("output", "output/wordcount-result.txt");
        
        System.out.println("输入文件路径: " + inputPath);
        System.out.println("输出文件路径: " + outputPath);
        
        // 读取文本文件
        DataSource<String> text = env.readTextFile(inputPath);
        
        // 执行WordCount处理流程
        text
            // 分词并转换为(word, 1)格式
            .flatMap(new Tokenizer())
            // 过滤空字符串
            .filter(tuple -> !tuple.f0.isEmpty())
            // 按单词分组并求和
            .groupBy(0)
            .sum(1)
            // 按词频降序排序
            .sortPartition(1, org.apache.flink.api.common.operators.Order.DESCENDING)
            // 输出结果
            .writeAsCsv(outputPath, "\n", " : ")
            .setParallelism(1); // 设置并行度为1，确保输出到单个文件
        
        // 执行程序
        env.execute("Improved Batch WordCount");
        
        System.out.println("WordCount程序执行完成！结果已保存到: " + outputPath);
    }
    
    /**
     * 分词器：将每行文本分割成单词，并转换为(word, 1)的元组
     */
    public static final class Tokenizer implements FlatMapFunction<String, Tuple2<String, Long>> {
        
        @Override
        public void flatMap(String line, Collector<Tuple2<String, Long>> out) {
            // 转换为小写并按空格分割
            String[] words = line.toLowerCase().split("\\W+");
            
            // 发出每个单词
            for (String word : words) {
                // 过滤空字符串和长度小于2的单词
                if (word.length() >= 2) {
                    out.collect(new Tuple2<>(word, 1L));
                }
            }
        }
    }
}
